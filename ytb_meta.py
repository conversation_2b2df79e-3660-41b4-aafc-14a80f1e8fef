import json
import time
import requests
import re
from loguru import logger
import pandas as pd
import concurrent.futures
from threading import Lock

# 全局锁用于文件写入
write_lock = Lock()

proxies = {
    'http': 'http://5851b070f69-zone-adam:<EMAIL>:7788',
}
headers = {
    'accept': '*/*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'origin': 'https://www.youtube.com',
    'pragma': 'no-cache',
    'priority': 'u=1, i',
    'referer': 'https://www.youtube.com/@fallontonight/videos',
    'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
    'sec-ch-ua-arch': '"x86"',
    'sec-ch-ua-bitness': '"64"',
    'sec-ch-ua-form-factors': '"Desktop"',
    'sec-ch-ua-full-version': '"135.0.7049.96"',
    'sec-ch-ua-full-version-list': '"Google Chrome";v="135.0.7049.96", "Not-A.Brand";v="8.0.0.0", "Chromium";v="135.0.7049.96"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-model': '""',
    'sec-ch-ua-platform': '"Windows"',
    'sec-ch-ua-platform-version': '"19.0.0"',
    'sec-ch-ua-wow64': '?0',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'same-origin',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'x-goog-authuser': '0',
    'x-origin': 'https://www.youtube.com',
    'x-youtube-bootstrap-logged-in': 'true',
    'x-youtube-client-name': '1',
    'x-youtube-client-version': '2.20250710.09.00',
}


def detail(dic_, write_file):
    nums = 1
    while nums < 5:
        try:
            params = {
                'v': dic_['video_id'],
            }

            response = requests.get('https://www.youtube.com/watch', params=params, headers=headers)
            if 'twoColumnWatchNextResults' in response.text:
                content = re.search(r'var ytInitialData = (.*?)</script>', response.text, re.S).group(1).strip(';')
                result = json.loads(content)
                for item in result['contents']['twoColumnWatchNextResults']['results']['results']['contents']:
                    for key, value in item.items():
                        if key == 'videoPrimaryInfoRenderer':
                            dic_['view_count'] = value['viewCount']['videoViewCountRenderer']['originalViewCount']
                            dic_['publish_time'] = value['dateText']['simpleText']
                            for item_ in value['videoActions']['menuRenderer']['topLevelButtons']:
                                for key_, value_ in item_.items():
                                    if key_ == 'segmentedLikeDislikeButtonViewModel':
                                        dic_['like_count'] = \
                                        value_['likeButtonViewModel']['likeButtonViewModel']['toggleButtonViewModel'][
                                            'toggleButtonViewModel']['toggledButtonViewModel']['buttonViewModel'][
                                            'title']
                                        dis = value_['dislikeButtonViewModel']['dislikeButtonViewModel'][
                                            'toggleButtonViewModel']['toggleButtonViewModel']['toggledButtonViewModel'][
                                            'buttonViewModel']['title']
                                        dic_['dislike_count'] = '0' if dis == '不喜欢' else dis
                                    if key_ == 'buttonViewModel':
                                        dic_['share_count'] = "0" if value_['title'] == '分享' else value_['title']
                        if key == 'videoSecondaryInfoRenderer':
                            dic_['tag_list'] = []
                            for tag in value['attributedDescription']['content'].split('\n')[0].split(' #'):
                                if '#' in tag:
                                    dic_['tag_list'].append(tag)
                                else:
                                    dic_['tag_list'].append("#" + tag)
                            dic_['description'] = value['attributedDescription']['content']
                            dic_['author'] = value['owner']['videoOwnerRenderer']['title']['runs'][0]['text']
                            dic_['subscriber_count'] = value['owner']['videoOwnerRenderer']['subscriberCountText'][
                                'simpleText']
                for item in result['engagementPanels']:
                    for key, value in item.items():
                        if key == 'engagementPanelSectionListRenderer' and 'engagementPanelTitleHeaderRenderer' in str(
                                value) and 'contextualInfo' in str(value):
                            dic_['comment_count'] = \
                            value['header']['engagementPanelTitleHeaderRenderer']['contextualInfo']['runs'][0]['text']

                # 使用锁确保线程安全的文件写入
                with write_lock:
                    with open(write_file, 'a+', encoding='utf-8') as f:
                        f.write(json.dumps(dic_, ensure_ascii=False) + '\n')
                logger.info(f'{dic_["video_id"]} === 写入成功')
                break
            else:
                nums += 1
        except Exception as e:
            logger.error(f'{dic_["video_id"]} === {e}')
            nums += 1


def get_id(continuation_, url_, write_file, max_workers=5):
    page = 2
    while True:
        try:
            params = {
                'prettyPrint': 'false',
            }

            json_data = {
                'context': {
                    'client': {
                        'hl': 'zh-CN',
                        'gl': 'US',
                        'deviceMake': '',
                        'deviceModel': '',
                        'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36,gzip(gfe)',
                        'clientName': 'WEB',
                        'clientVersion': '2.20250710.09.00',
                        'osName': 'Windows',
                        'osVersion': '10.0',
                        'originalUrl': f'{url_}/videos',
                        'screenPixelDensity': 2,
                        'platform': 'DESKTOP',
                        'clientFormFactor': 'UNKNOWN_FORM_FACTOR',
                        'screenDensityFloat': 1.5,
                        'userInterfaceTheme': 'USER_INTERFACE_THEME_LIGHT',
                        'timeZone': 'Asia/Shanghai',
                        'browserName': 'Chrome',
                        'browserVersion': '*********',
                        'acceptHeader': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                        'screenWidthPoints': 514,
                        'screenHeightPoints': 898,
                        'utcOffsetMinutes': 480,
                        'connectionType': 'CONN_CELLULAR_4G',
                        'memoryTotalKbytes': '8000000',
                        'mainAppWebInfo': {
                            'graftUrl': f'{url_}/videos',
                            'pwaInstallabilityStatus': 'PWA_INSTALLABILITY_STATUS_CAN_BE_INSTALLED',
                            'webDisplayMode': 'WEB_DISPLAY_MODE_BROWSER',
                            'isWebNativeShareAvailable': True,
                        },
                    },
                    'user': {
                        'lockedSafetyMode': False,
                    },
                    'request': {
                        'useSsl': True,
                        'internalExperimentFlags': [],
                        'consistencyTokenJars': [],
                    },
                    'adSignalsInfo': {
                        'params': [
                            {
                                'key': 'dt',
                                'value': f'{round(time.time() * 1000)}',
                            },
                            {
                                'key': 'flash',
                                'value': '0',
                            },
                            {
                                'key': 'frm',
                                'value': '0',
                            },
                            {
                                'key': 'u_tz',
                                'value': '480',
                            },
                            {
                                'key': 'u_his',
                                'value': '3',
                            },
                            {
                                'key': 'u_h',
                                'value': '1067',
                            },
                            {
                                'key': 'u_w',
                                'value': '1707',
                            },
                            {
                                'key': 'u_ah',
                                'value': '1019',
                            },
                            {
                                'key': 'u_aw',
                                'value': '1707',
                            },
                            {
                                'key': 'u_cd',
                                'value': '24',
                            },
                            {
                                'key': 'bc',
                                'value': '31',
                            },
                            {
                                'key': 'bih',
                                'value': '898',
                            },
                            {
                                'key': 'biw',
                                'value': '499',
                            },
                            {
                                'key': 'brdim',
                                'value': '0,0,0,0,1707,0,1707,1019,514,898',
                            },
                            {
                                'key': 'vis',
                                'value': '1',
                            },
                            {
                                'key': 'wgl',
                                'value': 'true',
                            },
                            {
                                'key': 'ca_type',
                                'value': 'image',
                            },
                        ],
                    },
                },
                'continuation': continuation_,
            }

            response = requests.post(
                'https://www.youtube.com/youtubei/v1/browse',
                params=params,
                headers=headers,
                json=json_data,
                # proxies=proxies
            )
            if 'appendContinuationItemsAction' not in response.text or 'onResponseReceivedActions' not in response.text:
                break

            video_list = []
            for item in response.json()['onResponseReceivedActions']:
                for key in item['appendContinuationItemsAction']['continuationItems'][:-1]:
                    video_id = key['richItemRenderer']['content']['videoRenderer']['videoId']
                    length_time = key['richItemRenderer']['content']['videoRenderer']['lengthText']['simpleText']
                    title = key['richItemRenderer']['content']['videoRenderer']['title']['runs'][0]['text']
                    dic = {
                        'video_id': video_id,
                        'length_time': length_time,
                        'title': title
                    }
                    video_list.append(dic)
                try:
                    continuation_ = \
                        item['appendContinuationItemsAction']['continuationItems'][-1]['continuationItemRenderer'][
                            'continuationEndpoint']['continuationCommand']['token']
                except KeyError:
                    continuation_ = ''

            # 使用线程池处理当前页的所有视频
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                futures = [executor.submit(detail, video, write_file) for video in video_list]
                concurrent.futures.wait(futures)

            logger.info(f'{url_} === 第{page}页 === success (共{len(video_list)}个视频)')
            page += 1
            if not continuation_:
                logger.info(f'{url_} === 全部完成')
                break
        except Exception as e:
            logger.error(f'{url_} === 第{page}页 === {e}')
            time.sleep(5)


def search(url_, write_file, max_workers=5):
    url__ = url_.strip("/")
    response = requests.get(f'{url__}/videos', headers=headers)
    continuation = re.search(r'{"token":"(.*?)","', response.text, re.S).group(1)
    content = re.search(r'var ytInitialData = (.*?)</script>', response.text, re.S).group(1).strip(';')

    video_list = []
    for tab in json.loads(content)['contents']['twoColumnBrowseResultsRenderer']['tabs']:
        if 'richGridRenderer' in str(tab):
            for ii in tab['tabRenderer']['content']['richGridRenderer']['contents'][:-1]:
                video_id = ii['richItemRenderer']['content']['videoRenderer']['videoId']
                length_time = ii['richItemRenderer']['content']['videoRenderer']['lengthText']['simpleText']
                title = ii['richItemRenderer']['content']['videoRenderer']['title']['runs'][0]['text']
                dic = {
                    'video_id': video_id,
                    'length_time': length_time,
                    'title': title
                }
                video_list.append(dic)

    # 使用线程池处理第一页的所有视频
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(detail, video, write_file) for video in video_list]
        concurrent.futures.wait(futures)

    logger.info(f'{url_} === 第1页 === success (共{len(video_list)}个视频)')
    get_id(continuation, url__, write_file, max_workers)


def process_channel(url, write_file, max_workers):
    search(url.split('?')[0], write_file, max_workers)


if __name__ == '__main__':
    df = pd.read_excel('./short_0718_ytb.xlsx')
    write_file = './short_youtube_0718_meta.jsonl'

    # 设置线程参数
    channel_max_workers = 2  # 同时处理的频道数
    video_max_workers = 5  # 每个频道同时处理的视频数

    urls = df['视频链接'].tolist()

    # 使用线程池处理所有频道
    with concurrent.futures.ThreadPoolExecutor(max_workers=channel_max_workers) as executor:
        futures = []
        for url in urls:
            futures.append(executor.submit(process_channel, url, write_file, video_max_workers))

        # 等待所有频道完成
        for future in concurrent.futures.as_completed(futures):
            try:
                future.result()
            except Exception as e:
                logger.error(f"处理频道时出错: {e}")

    logger.info('所有连接全部完成')