import concurrent.futures
import argparse
import threading
import os
from utils.downloader import download_video
from utils.uploader import upload_video
from config import UPLOAD_TO_S3, UPLOAD_TO_TOS, MAX_WORKERS, DATA_DIR

# Create a lock to prevent race conditions when writing to files
file_lock = threading.Lock()

def get_processed_ids_from_log(file_path):
    """
    Reads a log file where each line is expected to be a video ID.
    Returns a set of unique video IDs.
    """
    print(f"INFO: Reading processed IDs from: {file_path}")
    if not os.path.exists(file_path):
        print(f"INFO: Log file not found: {file_path}")
        return set()
    with open(file_path, 'r', encoding='utf-8') as f:
        # Each line in the log file is assumed to be a unique video ID
        processed_ids = {line.strip() for line in f if line.strip()}
    print(f"INFO: Loaded {len(processed_ids)} unique IDs from {os.path.basename(file_path)}.")
    return processed_ids


def resume_interrupted_uploads(data_dir):
    """
    Finds downloaded but not uploaded files and resumes their upload.
    """
    print("INFO: Checking for interrupted uploads...")
    processed_log_path = os.path.join(data_dir, "processed_video.txt")
    downloads_dir = os.path.join(data_dir, "downloads")

    # Get IDs of already uploaded videos
    uploaded_video_ids = get_processed_ids_from_log(processed_log_path)

    if not os.path.exists(downloads_dir):
        print("INFO: Downloads directory does not exist. No interrupted uploads to resume.")
        return

    resumed_count = 0
    for filename in os.listdir(downloads_dir):
        # Process only video files, not .json files etc.
        if filename.endswith(('.mp4', '.mkv', '.webm')):
            # Extract video ID from local filename, e.g., 'VIDEO_ID' from 'VIDEO_ID_audio_video.mp4'
            local_video_id = filename.split('_')[0]
            
            if local_video_id not in uploaded_video_ids:
                print(f"INFO: Found interrupted upload: {filename}. Resuming upload...")
                file_path = os.path.join(downloads_dir, filename)
                upload_video(file_path, file_lock, data_dir)
                resumed_count += 1
    
    if resumed_count == 0:
        print("INFO: No interrupted uploads found.")
    else:
        print(f"INFO: Resumed {resumed_count} interrupted uploads.")
    print("INFO: Finished checking for interrupted uploads.")


def process_video(video_url, data_dir, cookies_file=None):
    """
    Downloads a video and then uploads it.
    """
    print(f"INFO: Starting to process video: {video_url}")
    downloaded_file_path = download_video(video_url, file_lock, data_dir, cookies_file)
    if downloaded_file_path:
        print(f"INFO: Video downloaded, starting upload: {downloaded_file_path}")
        upload_video(downloaded_file_path, file_lock, data_dir)
    else:
        print(f"WARN: Download failed for {video_url}, skipping upload.")


if __name__ == "__main__":
    print("INFO: Application starting...")
    parser = argparse.ArgumentParser(description="Download and upload videos from a list of URLs.")
    parser.add_argument('-f', '--file', default='video_urls.txt', help='Path to the file containing video URLs.')
    parser.add_argument('-d', '--data-dir', default=DATA_DIR, help='Directory to store downloaded videos and logs.')
    parser.add_argument('--cookies', help='Path to the cookies file.')
    args = parser.parse_args()
    print(f"INFO: Arguments: file='{args.file}', data-dir='{args.data_dir}', cookies='{args.cookies}'")

    data_dir = args.data_dir
    if not os.path.exists(data_dir):
        print(f"INFO: Creating data directory: {data_dir}")
        os.makedirs(data_dir)
    downloads_path = os.path.join(data_dir, 'downloads')
    if not os.path.exists(downloads_path):
        print(f"INFO: Creating downloads directory: {downloads_path}")
        os.makedirs(downloads_path)

    # Resume any uploads that were interrupted before processing new videos
    if UPLOAD_TO_S3 or UPLOAD_TO_TOS:
        resume_interrupted_uploads(args.data_dir)

    print("INFO: Loading processed video IDs to avoid re-downloading.")
    processed_downloaded = get_processed_ids_from_log(os.path.join(data_dir, 'downloaded_video.txt'))
    if processed_downloaded:
        print(f"INFO: Found {len(processed_downloaded)} downloaded video IDs: {', '.join(processed_downloaded)}")

    processed_uploaded = get_processed_ids_from_log(os.path.join(data_dir, 'processed_video.txt'))
    if processed_uploaded:
        print(f"INFO: Found {len(processed_uploaded)} uploaded video IDs: {', '.join(processed_uploaded)}")

    processed_ids = processed_downloaded.union(processed_uploaded)
    print(f"INFO: Total unique processed video IDs loaded: {len(processed_ids)}")
    if processed_ids:
        print(f"INFO: All unique processed IDs: {', '.join(processed_ids)}")

    try:
        print(f"INFO: Reading video URLs from: {args.file}")
        with open(args.file, 'r') as f:
            video_urls = []
            total_urls_in_file = 0
            skipped_count = 0
            for line in f:
                total_urls_in_file += 1
                line = line.strip()
                if not line:
                    continue
                
                # Extract video ID from URL or line
                video_id = line
                if '=' in line:
                    video_id = line.split('=')[-1]
                
                if '&' in video_id: # Handle cases like ...v=VIDEO_ID&list=...
                    video_id = video_id.split('&')[0]

                if video_id in processed_ids:
                    print(f"INFO: Skipping already processed video ID: {video_id}")
                    skipped_count += 1
                    continue

                if line.startswith('http'):
                    video_urls.append(line)
                else:
                    video_urls.append(f"https://www.youtube.com/watch?v={line}")
        
        print(f"INFO: Found {total_urls_in_file} URLs in file. Skipped {skipped_count} already processed. Adding {len(video_urls)} new videos to the queue.")

    except FileNotFoundError:
        print(f"ERROR: The file '{args.file}' was not found.")
        exit(1)

    if not video_urls:
        print("INFO: No new videos to process.")
    else:
        print(f"INFO: Starting concurrent download and upload with {MAX_WORKERS} workers.")
        with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_url = {executor.submit(process_video, url, data_dir, args.cookies): url for url in video_urls}
            for future in concurrent.futures.as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    future.result()
                    print(f"INFO: Successfully processed: {url}")
                except Exception as exc:
                    print(f'ERROR: {url} generated an exception: {exc}')
    
    print("INFO: Application finished.")