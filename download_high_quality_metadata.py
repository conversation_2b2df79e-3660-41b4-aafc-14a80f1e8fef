import subprocess
import json
import os
import sys
import time
import random
import string
from datetime import datetime
import threading
import signal
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import logging
from logging.handlers import RotatingFileHandler

# 配置文件
JSONL_FILE = "/root/novel/ytb_bozhu/all_result.jsonl"
OUTPUT_DIR = "./metadata_downloads"
SUCCESS_LOG = "logs/metadata_success.log"
FAIL_LOG = "logs/metadata_fail.log"
MAIN_LOG = "logs/download_metadata.log"

# 代理配置
PROXY_HOST = "novabpm-ins-7ws6jjkn.novada.pro"
PROXY_PORT = 7788
PROXY_USER = "5851b070f69-zone-adam"
PROXY_PASS = "t5gNbn29fwu2Rlhc"


'''
1G不限量套餐 到期时间 2025-09-02

每次请求更换
curl -x novabpm-ins-7ws6jjkn.novada.pro:7788 -U "5851b070f69-zone-adam:t5gNbn29fwu2Rlhc" ipinfo.novada.pro

按session更换
curl -x novabpm-ins-7ws6jjkn.novada.pro:7788 -U "5851b070f69-zone-adam-session-16位随机的大小写字母加数字:t5gNbn29fwu2Rlhc" ipinfo.novada.pro

'''
# 并发配置
MAX_WORKERS = 30
DELAY_BETWEEN_DOWNLOADS = 1

# 超时配置
FORMAT_TIMEOUT = 30  # 获取格式信息超时时间（秒）
DOWNLOAD_TIMEOUT = 60  # 下载元信息超时时间（秒）
MAX_CONSECUTIVE_TIMEOUTS = 5  # 最大连续超时次数
TIMEOUT_RECOVERY_DELAY = 10  # 超时恢复延迟（秒）

# 重试配置
MAX_RETRY_ATTEMPTS = 2  # 超时后最大重试次数
RETRY_DELAY = 3  # 重试间隔（秒）

# 全局变量
log_lock = threading.Lock()
shutdown_event = threading.Event()
executor = None
running_processes = set()
shutdown_in_progress = False
process_cleanup_lock = threading.Lock()

# 统计信息
stats_lock = threading.Lock()
total_processed = 0
total_success = 0
total_failed = 0

# 超时管理
timeout_lock = threading.Lock()
consecutive_timeouts = 0
last_timeout_time = 0

# 日志配置
logger = None


def setup_logger():
    """设置日志配置"""
    global logger

    # 确保日志目录存在
    os.makedirs(os.path.dirname(MAIN_LOG), exist_ok=True)

    # 创建logger
    logger = logging.getLogger('download_metadata')
    logger.setLevel(logging.INFO)

    # 避免重复添加handler
    if logger.handlers:
        return logger

    # 创建文件handler，使用RotatingFileHandler避免日志文件过大
    file_handler = RotatingFileHandler(
        MAIN_LOG,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)

    # 创建控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加handler到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


def signal_handler(signum, _):
    """信号处理函数"""
    global shutdown_in_progress

    if shutdown_in_progress:
        logger.warning("强制退出...")
        os._exit(1)

    shutdown_in_progress = True
    logger.info(f"收到信号 {signum}，正在关闭程序...")
    shutdown_event.set()

    global executor
    if executor:
        logger.info("正在关闭进程池...")
        try:
            executor.shutdown(wait=False)
        except:
            pass

    cleanup_processes()
    logger.info("程序已关闭")
    os._exit(0)


def safe_terminate_process(process, timeout=5):
    """安全地终止单个进程，避免僵尸进程"""
    if not process or process.poll() is not None:
        return True

    try:
        logger.info(f"正在终止进程 {process.pid}...")
        # 首先尝试优雅终止
        process.terminate()
        try:
            process.wait(timeout=timeout)
            logger.info(f"进程 {process.pid} 已优雅终止")
            return True
        except subprocess.TimeoutExpired:
            # 如果优雅终止失败，强制杀死
            logger.warning(f"强制杀死进程 {process.pid}...")
            process.kill()
            try:
                process.wait(timeout=2)
                logger.info(f"进程 {process.pid} 已强制终止")
                return True
            except subprocess.TimeoutExpired:
                logger.error(f"警告: 无法终止进程 {process.pid}")
                return False
    except Exception as e:
        logger.error(f"终止进程 {process.pid} 时出错: {e}")
        return False


def cleanup_process_from_set(pid):
    """从运行进程集合中安全移除进程"""
    with process_cleanup_lock:
        running_processes.discard(pid)


def cleanup_processes():
    """清理所有运行中的子进程"""
    try:
        with process_cleanup_lock:
            if not running_processes:
                return

            logger.info(f"发现 {len(running_processes)} 个运行中的进程，正在清理...")

            # 复制集合以避免在迭代时修改
            pids_to_clean = running_processes.copy()

        for pid in pids_to_clean:
            try:
                process = psutil.Process(pid)
                logger.info(f"终止进程 {pid}")
                process.terminate()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                cleanup_process_from_set(pid)

        # 等待进程终止
        time.sleep(2)

        # 检查仍然存活的进程
        with process_cleanup_lock:
            remaining_pids = running_processes.copy()

        for pid in remaining_pids:
            try:
                process = psutil.Process(pid)
                if process.is_running():
                    logger.warning(f"强制杀死进程 {pid}")
                    process.kill()
                cleanup_process_from_set(pid)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                cleanup_process_from_set(pid)

        logger.info("进程清理完成")
    except Exception as e:
        logger.error(f"清理进程时出错: {e}")


def write_log(log_file, message):
    """线程安全的日志写入"""
    with log_lock:
        with open(log_file, "a", encoding='utf-8') as f:
            f.write(message)


def random_session(length=16):
    """生成随机 session 名称"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def get_proxy_url(session_id=None):
    """生成代理URL，如果没有提供session_id则生成新的"""
    if session_id is None:
        session_id = random_session()
    return f"http://{PROXY_USER}-session-{session_id}:{PROXY_PASS}@{PROXY_HOST}:{PROXY_PORT}"


def load_successful_videos():
    """从成功日志中加载已经成功下载的video_id集合"""
    successful_videos = set()
    try:
        if os.path.exists(SUCCESS_LOG):
            with open(SUCCESS_LOG, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and '|' in line:
                        # 解析日志行格式: timestamp | video_id | session=xxx | SUCCESS | time=xxx
                        parts = line.split('|')
                        if len(parts) >= 2:
                            video_id = parts[1].strip()
                            if video_id:
                                successful_videos.add(video_id)
        logger.info(f"已加载 {len(successful_videos)} 个已成功下载的视频ID")
    except Exception as e:
        logger.error(f"读取成功日志失败: {e}")
    return successful_videos


def update_stats(success=True):
    """更新统计信息"""
    global total_processed, total_success, total_failed
    with stats_lock:
        total_processed += 1
        if success:
            total_success += 1
        else:
            total_failed += 1


def handle_timeout():
    """处理超时情况，返回是否应该继续处理"""
    global consecutive_timeouts, last_timeout_time

    with timeout_lock:
        consecutive_timeouts += 1
        last_timeout_time = time.time()

        logger.warning(f"连续超时次数: {consecutive_timeouts}/{MAX_CONSECUTIVE_TIMEOUTS}")

        if consecutive_timeouts >= MAX_CONSECUTIVE_TIMEOUTS:
            logger.warning(f"连续超时次数过多，暂停 {TIMEOUT_RECOVERY_DELAY} 秒...")
            time.sleep(TIMEOUT_RECOVERY_DELAY)
            consecutive_timeouts = 0  # 重置计数器
            return True  # 继续处理，但已经暂停过了

        return True  # 继续处理


def reset_timeout_counter():
    """重置超时计数器（成功时调用）"""
    global consecutive_timeouts

    with timeout_lock:
        if consecutive_timeouts > 0:
            consecutive_timeouts = 0


def get_video_formats_with_retry(video_id, youtube_url, session_id):
    """带重试机制的格式信息获取"""
    last_error = None

    for attempt in range(MAX_RETRY_ATTEMPTS + 1):  # 原始尝试 + 重试次数
        try:
            # 每次尝试使用新的代理session
            if attempt > 0:
                session_id = random_session()
                logger.warning(f"格式信息获取重试 {attempt}/{MAX_RETRY_ATTEMPTS}: {video_id} - 使用新代理session")
                time.sleep(RETRY_DELAY)

            proxy_url = get_proxy_url(session_id)

            # 获取格式信息命令
            format_cmd = [
                "yt-dlp",
                "-F",  # 显示所有可用格式
                "--proxy", proxy_url,
                youtube_url
            ]

            logger.info(f"获取格式信息 (尝试 {attempt + 1}): {video_id}")
            format_process = subprocess.run(format_cmd, capture_output=True, text=True, timeout=FORMAT_TIMEOUT)

            if format_process.returncode == 0:
                logger.info(f"格式信息获取成功: {video_id} (尝试 {attempt + 1})")
                return format_process.stdout, session_id, None
            else:
                error_msg = format_process.stderr if format_process.stderr else "Unknown error"
                last_error = f"yt-dlp返回错误: {error_msg}"
                logger.error(f"格式信息获取失败 (尝试 {attempt + 1}): {video_id} - {last_error}")

                # 如果不是最后一次尝试，继续重试
                if attempt < MAX_RETRY_ATTEMPTS:
                    continue
                else:
                    return None, session_id, last_error

        except subprocess.TimeoutExpired:
            last_error = f"{FORMAT_TIMEOUT}秒超时"
            logger.error(f"格式信息获取超时 (尝试 {attempt + 1}): {video_id} - {last_error}")
            handle_timeout()

            # 如果不是最后一次尝试，继续重试
            if attempt < MAX_RETRY_ATTEMPTS:
                continue
            else:
                return None, session_id, last_error

        except Exception as e:
            last_error = f"异常: {e}"
            logger.error(f"格式信息获取异常 (尝试 {attempt + 1}): {video_id} - {last_error}")

            # 如果不是最后一次尝试，继续重试
            if attempt < MAX_RETRY_ATTEMPTS:
                continue
            else:
                return None, session_id, last_error

    return None, session_id, last_error


def print_stats():
    """打印统计信息"""
    with stats_lock:
        logger.info("=== 处理统计 ===")
        logger.info(f"总处理数: {total_processed}")
        logger.info(f"成功: {total_success}")
        logger.info(f"失败: {total_failed}")
        if total_processed > 0:
            success_rate = (total_success / total_processed) * 100
            logger.info(f"成功率: {success_rate:.2f}%")



def get_video_subdir(video_id):
    """根据video_id生成子目录路径，避免单个目录文件过多"""
    # 使用video_id的前2个字符作为一级目录，第3-4个字符作为二级目录，全部转为小写
    if len(video_id) >= 4:
        level1 = video_id[:2].lower()
        level2 = video_id[2:4].lower()
        return os.path.join(OUTPUT_DIR, level1, level2)
    elif len(video_id) >= 2:
        level1 = video_id[:2].lower()
        return os.path.join(OUTPUT_DIR, level1)
    else:
        return os.path.join(OUTPUT_DIR, "misc")


def download_video_metadata(video_info):
    """下载单个视频的元信息"""
    if shutdown_event.is_set():
        return False

    start_time = time.time()
    session_id = random_session()

    # 从video_info中提取video_id
    video_id = video_info.get('video_id', '')
    video_type = video_info.get('type', 'unknown')

    if not video_id:
        write_log(FAIL_LOG, f"{datetime.now()} | NO_VIDEO_ID | {video_info}\n")
        return False

    # 创建子目录
    video_subdir = get_video_subdir(video_id)
    os.makedirs(video_subdir, exist_ok=True)

    if video_type == 'short':
        youtube_url = f"https://www.youtube.com/shorts/{video_id}"
    else:
        youtube_url = f"https://www.youtube.com/watch?v={video_id}"

    # 使用带重试的格式信息获取
    format_output, final_session_id, error_msg = get_video_formats_with_retry(video_id, youtube_url, session_id)

    if format_output is None:
        # 所有重试都失败了
        if "超时" in error_msg:
            write_log(FAIL_LOG, f"{datetime.now()} | {video_id} | session={final_session_id} | FORMAT_TIMEOUT_ALL_RETRIES | error={error_msg}\n")
        else:
            write_log(FAIL_LOG, f"{datetime.now()} | {video_id} | session={final_session_id} | FORMAT_FAIL_ALL_RETRIES | error={error_msg}\n")
        return False

    # 解析格式信息
    high_quality_formats = parse_high_quality_formats(format_output)
    max_bitrate = high_quality_formats.get('max_bitrate', 0)
    bitrate_str = f"{int(max_bitrate)}k" if max_bitrate > 0 else "unknown"

    # 使用最终成功的session_id
    session_id = final_session_id
    proxy_url = get_proxy_url(session_id)

    # yt-dlp命令：下载视频元信息，文件名包含码率
    cmd = [
        "yt-dlp",
        "--list-formats",  # 列出所有格式
        "--write-info-json",  # 写入元信息JSON
        "--no-download",  # 不下载视频文件
        "--proxy", proxy_url,
        "-o", f"{video_subdir}/{video_id}_{video_type}_{bitrate_str}_metadata.%(ext)s",
        youtube_url
    ]
    
    process = None
    try:
        # 启动子进程
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        running_processes.add(process.pid)

        # 等待进程完成，设置超时
        download_timeout = DOWNLOAD_TIMEOUT
        elapsed_time = 0

        while process.poll() is None:
            if shutdown_event.is_set():
                logger.info(f"中断下载: {video_id}")
                safe_terminate_process(process)
                cleanup_process_from_set(process.pid)
                return False

            if elapsed_time >= download_timeout:
                logger.error(f"下载超时: {video_id} - {download_timeout}秒")
                safe_terminate_process(process)
                cleanup_process_from_set(process.pid)
                write_log(FAIL_LOG, f"{datetime.now()} | {video_id} | session={session_id} | DOWNLOAD_TIMEOUT | timeout={download_timeout}s\n")
                handle_timeout()
                return False

            time.sleep(0.1)
            elapsed_time += 0.1

        cleanup_process_from_set(process.pid)
        
        if process.returncode == 0:

            # 保存格式信息到文件
            format_info = {
                'video_id': video_id,
                'video_type': video_type,
                'youtube_url': youtube_url,
                'original_info': video_info,
                'high_quality_formats': high_quality_formats,
                'download_time': datetime.now().isoformat(),
                'processing_time': time.time() - start_time
            }

            format_file = f"{video_subdir}/{video_id}_{video_type}_{bitrate_str}_formats.json"
            with open(format_file, 'w', encoding='utf-8') as f:
                json.dump(format_info, f, ensure_ascii=False, indent=2)

            write_log(SUCCESS_LOG, f"{datetime.now()} | {video_id} | session={session_id} | SUCCESS | time={time.time()-start_time:.2f}s\n")
            logger.info(f"成功处理: {video_id} - {time.time()-start_time:.2f}s")
            update_stats(True)
            reset_timeout_counter()  # 成功时重置超时计数器
            return True
        else:
            stderr_output = process.stderr.read() if process.stderr else "Unknown error"
            write_log(FAIL_LOG, f"{datetime.now()} | {video_id} | session={session_id} | FAIL | error={stderr_output}\n")
            logger.error(f"处理失败: {video_id}")
            update_stats(False)
            return False
            
    except Exception as e:
        if process:
            safe_terminate_process(process)
            cleanup_process_from_set(process.pid)
        write_log(FAIL_LOG, f"{datetime.now()} | {video_id} | session={session_id} | EXCEPTION: {e}\n")
        logger.error(f"处理异常: {video_id} - {e}")
        update_stats(False)
        return False


def parse_high_quality_formats(format_output):
    """解析并获取最高码率的格式信息"""
    all_formats = []
    lines = format_output.split('\n')

    for line in lines:
        if 'mp4' in line or 'webm' in line:
            # 解析格式行，寻找码率信息
            parts = line.split()
            for part in parts:
                if 'k' in part and part.replace('k', '').replace('.', '').isdigit():
                    try:
                        bitrate = float(part.replace('k', ''))
                        all_formats.append({
                            'format_line': line.strip(),
                            'estimated_bitrate': bitrate
                        })
                        break
                    except ValueError:
                        continue

    # 按码率排序，获取最高码率的格式
    if all_formats:
        all_formats.sort(key=lambda x: x['estimated_bitrate'], reverse=True)
        # 返回最高码率的格式，以及前几个高码率格式作为备选
        max_bitrate = all_formats[0]['estimated_bitrate']
        high_quality_formats = [fmt for fmt in all_formats if fmt['estimated_bitrate'] >= max_bitrate * 0.9]
        return {
            'highest_bitrate_format': all_formats[0],
            'top_quality_formats': high_quality_formats[:5],  # 返回前5个高质量格式
            'all_formats_count': len(all_formats),
            'max_bitrate': max_bitrate
        }

    return {
        'highest_bitrate_format': None,
        'top_quality_formats': [],
        'all_formats_count': 0,
        'max_bitrate': 0
    }


def download_with_delay(video_info):
    """带延迟的下载函数"""
    time.sleep(random.uniform(0, DELAY_BETWEEN_DOWNLOADS))
    return download_video_metadata(video_info)


def read_jsonl_file(file_path):
    """读取包含video_id和short_id的JSON文件"""
    video_list = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:
                    try:
                        data = json.loads(line)

                        # 处理video_id数组
                        if 'video_id' in data and isinstance(data['video_id'], list):
                            for vid in data['video_id']:
                                video_info = {
                                    'video_id': vid,
                                    'type': 'video',
                                    'original_data': data
                                }
                                video_list.append(video_info)

                        # 处理short_id数组
                        if 'short_id' in data and isinstance(data['short_id'], list):
                            for sid in data['short_id']:
                                video_info = {
                                    'video_id': sid,
                                    'type': 'short',
                                    'original_data': data
                                }
                                video_list.append(video_info)

                    except json.JSONDecodeError as e:
                        logger.error(f"解析第{line_num}行JSON失败: {e}")
                        continue
        return video_list
    except FileNotFoundError:
        logger.error(f"文件不存在: {file_path}")
        return []
    except Exception as e:
        logger.error(f"读取文件失败: {e}")
        return []


def main():
    """主函数"""
    # 设置日志
    setup_logger()

    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 设置进程组，便于清理
    try:
        os.setpgrp()
    except:
        pass

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 读取JSONL文件
    logger.info(f"正在读取文件: {JSONL_FILE}")
    video_list = read_jsonl_file(JSONL_FILE)

    if not video_list:
        logger.error("没有找到有效的视频信息")
        return

    logger.info(f"找到 {len(video_list)} 个视频")

    # 加载已成功下载的视频ID
    successful_videos = load_successful_videos()

    # 过滤掉已经成功下载的视频
    original_count = len(video_list)
    video_list = [video for video in video_list if video.get('video_id') not in successful_videos]
    filtered_count = len(video_list)

    logger.info(f"过滤后剩余 {filtered_count} 个视频需要处理 (跳过了 {original_count - filtered_count} 个已成功的视频)")

    if not video_list:
        logger.info("所有视频都已经成功下载，无需处理")
        return
    
    global executor
    batch_size = min(100, len(video_list))  # 分批处理，每批最多100个
    processed_count = 0

    try:
        # 分批处理视频列表
        for batch_start in range(0, len(video_list), batch_size):
            if shutdown_event.is_set():
                logger.info("收到关闭信号，停止处理...")
                break

            batch_end = min(batch_start + batch_size, len(video_list))
            current_batch = video_list[batch_start:batch_end]

            logger.info(f"处理批次 {batch_start//batch_size + 1}: 视频 {batch_start+1}-{batch_end}")

            # 为每个批次创建新的线程池
            executor = ThreadPoolExecutor(max_workers=MAX_WORKERS)

            try:
                # 提交当前批次的任务
                future_to_video = {executor.submit(download_with_delay, video_info): video_info for video_info in current_batch}

                # 处理完成的任务
                batch_completed = 0
                for future in as_completed(future_to_video):
                    if shutdown_event.is_set():
                        logger.info("收到关闭信号，停止处理...")
                        break

                    video_info = future_to_video[future]
                    try:
                        future.result(timeout=1)
                        batch_completed += 1
                        processed_count += 1

                        # 每处理10个视频打印一次进度
                        if processed_count % 10 == 0:
                            logger.info(f"已处理 {processed_count}/{len(video_list)} 个视频")

                    except Exception as exc:
                        video_id = video_info.get('video_id', 'unknown')
                        video_type = video_info.get('type', 'unknown')
                        logger.error(f"处理异常: {video_id} ({video_type}) - {exc}")
                        write_log(FAIL_LOG, f"{datetime.now()} | {video_id} | {video_type} | FUTURE_EXCEPTION: {exc}\n")
                        update_stats(False)
                        processed_count += 1

                logger.info(f"批次完成: {batch_completed}/{len(current_batch)} 个视频成功")

            finally:
                # 关闭当前批次的线程池
                if executor:
                    try:
                        executor.shutdown(wait=True)
                    except Exception as e:
                        logger.error(f"关闭线程池异常: {e}")
                    executor = None

                # 清理当前批次的残留进程
                if running_processes:
                    logger.info(f"清理批次残留的 {len(running_processes)} 个子进程...")
                    cleanup_processes()

                # 批次间短暂休息
                if not shutdown_event.is_set() and batch_end < len(video_list):
                    logger.info("批次间休息2秒...")
                    time.sleep(2)
    
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号...")
        shutdown_event.set()
    except Exception as e:
        logger.error(f"程序异常: {e}")
        shutdown_event.set()
    finally:
        # 确保关闭线程池
        if executor:
            logger.info("正在关闭最后的线程池...")
            try:
                executor.shutdown(wait=True)
            except Exception as e:
                logger.error(f"关闭线程池异常: {e}")
            executor = None

        # 最终清理所有残留的子进程
        if running_processes:
            logger.info(f"最终清理残留的 {len(running_processes)} 个子进程...")
            cleanup_processes()

        logger.info("程序清理完成")

    print_stats()
    logger.info("处理完成")


if __name__ == "__main__":
    main()
