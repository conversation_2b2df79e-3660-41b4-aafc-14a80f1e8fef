import os
import tos
import config

# --- Configuration ---
TEST_FILE_NAME = "tos_test_file.txt"
TEST_FILE_CONTENT = "This is a test file for TOS upload."
TOS_TEST_FOLDER = "tos_test_folder/0830/"  # Use a trailing slash for folder structure

def run_tos_test():
    """
    Creates a local file, uploads it to a specific folder in TOS,
    and then verifies the contents of that folder.
    """
    # --- 1. Create a local dummy file ---
    print(f"Creating local test file: {TEST_FILE_NAME}")
    with open(TEST_FILE_NAME, "w") as f:
        f.write(TEST_FILE_CONTENT)
    local_file_size = os.path.getsize(TEST_FILE_NAME)
    print(f"Local file '{TEST_FILE_NAME}' created with size: {local_file_size} bytes.")

    # --- 2. Initialize TOS Client ---
    try:
        print("Initializing TOS client...")
        client = tos.TosClientV2(
            ak=config.TOS_ACCESS_KEY,
            sk=config.TOS_SECRET_KEY,
            endpoint=config.TOS_ENDPOINT,
            region=config.TOS_REGION
        )
        print("TOS client initialized successfully.")
    except Exception as e:
        print(f"Error: Failed to initialize TOS client. Please check your config.py.")
        print(f"Details: {e}")
        os.remove(TEST_FILE_NAME)
        return

    # --- 3. Upload the file ---
    object_key = f"{TOS_TEST_FOLDER}{TEST_FILE_NAME}"
    print(f"Uploading '{TEST_FILE_NAME}' to TOS bucket '{config.TOS_BUCKET_NAME}' as '{object_key}'...")
    try:
        result = client.put_object_from_file(
            bucket=config.TOS_BUCKET_NAME,
            key=object_key,
            file_path=TEST_FILE_NAME
        )
        if result.status_code == 200:
            print("Upload successful.")
        else:
            print(f"Upload failed. Status: {result.status_code}, Request ID: {result.request_id}")
            os.remove(TEST_FILE_NAME)
            return
    except Exception as e:
        print(f"An error occurred during upload: {e}")
        os.remove(TEST_FILE_NAME)
        return

    # --- 4. Verify by listing files in the folder ---
    print(f"\nVerifying files in TOS folder: '{TOS_TEST_FOLDER}'")
    try:
        total_files = 0
        total_size = 0
        
        resp = client.list_objects(
            bucket=config.TOS_BUCKET_NAME,
            prefix=TOS_TEST_FOLDER
        )

        print("--- Files in Folder ---")
        if resp.contents:
            for obj in resp.contents:
                print(f"- Key: {obj.key}, Size: {obj.size} bytes")
                total_files += 1
                total_size += obj.size
        else:
            print("No files found in the folder.")
        print("-----------------------")

        print(f"\nVerification complete.")
        print(f"Total files found: {total_files}")
        print(f"Total size of files: {total_size} bytes")

    except Exception as e:
        print(f"An error occurred during verification: {e}")

    # --- 5. Clean up local file ---
    finally:
        print(f"\nCleaning up local file: {TEST_FILE_NAME}")
        if os.path.exists(TEST_FILE_NAME):
            os.remove(TEST_FILE_NAME)
            print("Local file removed.")

if __name__ == "__main__":
    run_tos_test()