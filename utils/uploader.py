import os
import time
import boto3
import tos
from botocore.client import Config
from botocore.exceptions import ClientError

# Import all necessary configurations
from config import (
    UPLOAD_TO_S3,
    S3_ENDPOINT_URL,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    S3_BUCKET,
    S3_PREFIX,
    
    UPLOAD_TO_TOS,
    TOS_ENDPOINT,
    TOS_REGION,
    TOS_ACCESS_KEY,
    TOS_SECRET_KEY,
    TOS_BUCKET_NAME,
    TOS_PREFIX,

    BOTO_CONNECT_TIMEOUT,
    BOTO_READ_TIMEOUT,
    DELETE_LOCAL_VIDEO
)

def _upload_to_s3_compatible(file_path):
    """
    Uploads a file to an S3-compatible service.
    Returns (True, object_key) on success, (False, None) on failure.
    """
    if not all([S3_ENDPOINT_URL, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, S3_BUCKET]):
        print("S3 credentials are not fully configured. Skipping S3 upload.")
        return False, None

    try:
        s3_client = boto3.client(
            's3',
            endpoint_url=S3_ENDPOINT_URL,
            aws_access_key_id=AWS_ACCESS_KEY_ID,
            aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
            config=Config(
                connect_timeout=BOTO_CONNECT_TIMEOUT,
                read_timeout=BOTO_READ_TIMEOUT,
                signature_version='s3v4'
            )
        )
        
        object_key = f"{S3_PREFIX}{os.path.basename(file_path)}"
        file_size_bytes = os.path.getsize(file_path)
        file_size_mb = file_size_bytes / (1024 * 1024)
        
        print(f"Uploading {file_path} ({file_size_mb:.2f} MB) to S3 compatible storage: s3://{S3_BUCKET}/{object_key}")
        
        start_time = time.time()
        s3_client.upload_file(file_path, S3_BUCKET, object_key)
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"Successfully uploaded to S3 compatible storage in {duration:.2f} seconds. Object key: {object_key}")
        return True, object_key

    except ClientError as e:
        print(f"Failed to upload {file_path} to S3 compatible storage. Boto3 ClientError: {e}")
        return False, None
    except Exception as e:
        print(f"Failed to upload {file_path} to S3 compatible storage. An unexpected error occurred: {e}")
        return False, None

def _upload_to_tos_native(file_path):
    """
    Uploads a file to Volcengine TOS using the native SDK.
    Returns (True, object_key) on success, (False, None) on failure.
    """
    if not all([TOS_ENDPOINT, TOS_REGION, TOS_ACCESS_KEY, TOS_SECRET_KEY, TOS_BUCKET_NAME]):
        print("TOS credentials are not fully configured. Skipping TOS upload.")
        return False, None

    try:
        client = tos.TosClientV2(
            ak=TOS_ACCESS_KEY,
            sk=TOS_SECRET_KEY,
            endpoint=TOS_ENDPOINT,
            region=TOS_REGION
        )
        
        object_key = f"{TOS_PREFIX}{os.path.basename(file_path)}"
        file_size_bytes = os.path.getsize(file_path)
        file_size_mb = file_size_bytes / (1024 * 1024)
        
        print(f"Uploading {file_path} ({file_size_mb:.2f} MB) to TOS: tos://{TOS_BUCKET_NAME}/{object_key}")
        
        start_time = time.time()
        result = client.put_object_from_file(
            bucket=TOS_BUCKET_NAME,
            key=object_key,
            file_path=file_path
        )
        end_time = time.time()
        duration = end_time - start_time
        
        if result.status_code == 200:
            print(f"Successfully uploaded to TOS in {duration:.2f} seconds. Object key: {object_key}")
            return True, object_key
        else:
            print(f"Failed to upload to TOS. Status: {result.status_code}, Request ID: {result.request_id}")
            return False, None

    except Exception as e:
        print(f"Failed to upload {file_path} to TOS. An unexpected error occurred: {e}")
        return False, None

def upload_video(file_path, file_lock, data_dir):
    """
    Uploads a file to configured storage services (S3, TOS) based on config flags.
    Deletes the local file only if all enabled uploads are successful.

    Args:
        file_path (str): The path of the file to upload.
        file_lock (threading.Lock): Lock for thread-safe file operations.
        data_dir (str): The base data directory.
    """
    print(f"INFO: Starting upload for {file_path}")
    start_time = time.time()

    if not UPLOAD_TO_S3 and not UPLOAD_TO_TOS:
        print("All uploads are disabled. Skipping.")
        return

    upload_statuses = []
    uploaded_keys = []

    if UPLOAD_TO_S3:
        success, key = _upload_to_s3_compatible(file_path)
        upload_statuses.append(success)
        if success:
            uploaded_keys.append(f"s3://{S3_BUCKET}/{key}")

    if UPLOAD_TO_TOS:
        success, key = _upload_to_tos_native(file_path)
        upload_statuses.append(success)
        if success:
            uploaded_keys.append(f"tos://{TOS_BUCKET_NAME}/{key}")

    # Check if all enabled uploads were successful
    all_successful = all(upload_statuses)

    if all_successful and uploaded_keys:
        print("All enabled uploads completed successfully.")
        # Log the video ID as processed
        video_filename = os.path.basename(file_path)
        video_id = video_filename.split('_')[0]
        with file_lock:
            processed_log_path = os.path.join(data_dir, "processed_video.txt")
            with open(processed_log_path, "a", encoding='utf-8') as f:
                f.write(video_id + "\n")
        print(f"Logged as processed: {video_id}")
        print(f"Uploaded to: {', '.join(uploaded_keys)}")

        if DELETE_LOCAL_VIDEO:
            try:
                os.remove(file_path)
                print(f"Deleted local file: {file_path}")
            except OSError as e:
                print(f"Error deleting local file {file_path}: {e}")
        else:
            print(f"Kept local file as per config: {file_path}")
    else:
        print(f"One or more uploads failed for {file_path}. Local file will be kept for retry.")

    end_time = time.time()
    duration = end_time - start_time
    file_size_bytes = os.path.getsize(file_path)
    file_size_mb = file_size_bytes / (1024 * 1024)
    print(f"INFO: Finished upload for {file_path}. Total time: {duration:.2f} seconds, File size: {file_size_mb:.2f} MB")